<template>
  <view class="result-container">
    <!-- 现代化头部区域 -->
    <view class="hero-header">
      <view class="hero-background"></view>
      <view class="hero-content">
        <view class="success-indicator">
          <view class="success-icon">
            <text class="check-icon">✓</text>
            <view class="success-glow"></view>
          </view>
          <view class="success-content">
            <text class="success-title">视频字幕制作完成</text>
            <text class="success-desc">AI智能识别完毕，字幕已精准生成</text>
          </view>
        </view>
        <view class="hero-decoration">
          <view class="decoration-circle circle-1"></view>
          <view class="decoration-circle circle-2"></view>
          <view class="decoration-circle circle-3"></view>
        </view>
      </view>
    </view>

    <!-- 内容卡片区域 -->
    <view class="content-wrapper">
      <!-- 视频预览卡片 -->
      <view class="media-card">
        <view class="card-header">
          <view class="header-content">
            <text class="card-title">视频预览</text>
            <view class="card-badge">
              <text class="badge-text">HD</text>
            </view>
          </view>
          <view class="card-divider"></view>
        </view>
        <view class="video-wrapper">
          <view class="video-container">
            <video v-if="videoUrl" :src="videoUrl" controls class="video-player"></video>
            <view v-else class="video-placeholder">
              <view class="loading-animation">
                <text class="loading-icon">🎬</text>
                <view class="pulse-ring"></view>
              </view>
              <text class="loading-text">视频加载中...</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 字幕内容卡片 -->
      <view class="subtitle-card">
        <view class="card-header">
          <view class="header-content">
            <text class="card-title">字幕内容</text>
            <view class="subtitle-count">
              <text class="count-icon">📝</text>
              <text class="count-text">{{ subtitles.length }}条</text>
            </view>
          </view>
          <view class="card-divider"></view>
        </view>
        <scroll-view class="subtitle-scroll" scroll-y="true">
          <view class="subtitle-list">
            <view class="subtitle-item" v-for="(item, index) in subtitles" :key="index">
              <view class="subtitle-index">
                <text class="index-number">{{ index + 1 }}</text>
              </view>
              <view class="subtitle-content">
                <text class="subtitle-time">{{ item.startTime }} - {{ item.endTime }}</text>
                <text class="subtitle-text">{{ item.text }}</text>
              </view>
            </view>
            <view v-if="subtitles.length === 0" class="empty-subtitle">
              <view class="empty-icon">
                <text class="empty-emoji">📝</text>
              </view>
              <text class="empty-text">暂无字幕内容</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="action-footer">
      <button @click="downloadVideo" class="download-btn">
        <view class="btn-content">
          <text class="btn-icon">💾</text>
          <text class="btn-text">保存到相册</text>
        </view>
        <view class="btn-glow"></view>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 字幕数据
const subtitles = ref([])
const taskId = ref('')
const videoUrl = ref('')

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.taskId) {
    taskId.value = options.taskId
    console.log('接收到任务ID:', taskId.value)

    // 加载处理结果
    loadResult()
  } else {
    uni.showModal({
      title: '参数错误',
      content: '缺少任务ID参数，请重新处理视频',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  }
})

// 加载处理结果
const loadResult = async () => {
  try {
    uni.showLoading({
      title: '加载中...'
    })

    console.log('开始加载任务结果, taskId:', taskId.value)

    // 调用云函数获取处理结果
    const result = await uniCloud.callFunction({
      name: 'get-task-result',
      data: {
        taskId: taskId.value
      }
    })

    console.log('云函数返回结果:', {
      code: result.result?.code,
      message: result.result?.message,
      hasVideoUrl: !!result.result?.data?.videoUrl,
      videoUrlLength: result.result?.data?.videoUrl?.length || 0,
      subtitlesCount: result.result?.data?.subtitles?.length || 0
    })

    if (result.result.code === 200) {
      const { videoUrl: resultVideoUrl, subtitles: resultSubtitles } = result.result.data

      videoUrl.value = resultVideoUrl || ''
      subtitles.value = resultSubtitles || []

      console.log('数据设置完成:', {
        videoUrlSet: !!videoUrl.value,
        videoUrlDomain: videoUrl.value ? new URL(videoUrl.value).hostname : 'none',
        subtitlesCount: subtitles.value.length
      })

      uni.hideLoading()
    } else {
      console.error('云函数返回错误:', result.result)
      uni.hideLoading()
      uni.showToast({
        title: result.result.message || '加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('加载处理结果失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 检查相册写入权限
const checkWritePhotosAlbumPermission = (): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting
        if (authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，需要引导用户手动开启
          uni.showModal({
            title: '需要相册权限',
            content: '保存视频需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    resolve(settingRes.authSetting['scope.writePhotosAlbum'] === true)
                  },
                  fail: () => resolve(false)
                })
              } else {
                resolve(false)
              }
            }
          })
        } else if (authSetting['scope.writePhotosAlbum'] === undefined) {
          // 用户还没有授权过，直接返回true，让saveVideoToPhotosAlbum触发授权
          resolve(true)
        } else {
          // 用户已经授权
          resolve(true)
        }
      },
      fail: () => resolve(false)
    })
  })
}

// 下载视频
const downloadVideo = async () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频文件不存在',
      icon: 'none'
    })
    return
  }

  try {
    // 先检查相册权限
    const hasPermission = await checkWritePhotosAlbumPermission()
    if (!hasPermission) {
      uni.showToast({
        title: '需要相册权限才能保存视频',
        icon: 'none',
        duration: 3000
      })
      return
    }

    uni.showLoading({
      title: '准备下载...'
    })

    console.log('开始下载视频:', videoUrl.value)

    // 下载视频文件
    uni.downloadFile({
      url: videoUrl.value,
      timeout: 60000, // 设置60秒超时
      success: (res) => {
        console.log('下载响应:', res)

        if (res.statusCode === 200) {
          uni.showLoading({
            title: '保存中...'
          })

          // 保存到相册
          uni.saveVideoToPhotosAlbum({ 
            filePath: res.tempFilePath,
            success: () => {
              console.log('视频保存成功')
              uni.hideLoading()
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('保存视频失败:', err)
              uni.hideLoading()

              // 根据错误类型给出更具体的提示
              let errorMessage = '保存失败'
              if (err.errMsg) {
                if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
                  errorMessage = '请授权访问相册后重试'
                } else if (err.errMsg.includes('system error')) {
                  errorMessage = '系统错误，请稍后重试'
                } else if (err.errMsg.includes('file not exist')) {
                  errorMessage = '视频文件无效，请重新生成'
                } else {
                  errorMessage = `保存失败: ${err.errMsg}`
                }
              }

              uni.showModal({
                title: '保存失败',
                content: errorMessage,
                showCancel: true,
                confirmText: '重试',
                cancelText: '取消',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    // 用户选择重试
                    setTimeout(() => {
                      downloadVideo()
                    }, 500)
                  }
                }
              })
            }
          })
        } else {
          console.error('下载失败，状态码:', res.statusCode)
          uni.hideLoading()
          uni.showToast({
            title: `下载失败 (${res.statusCode})`,
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('下载视频失败:', err)
        uni.hideLoading()

        let errorMessage = '下载失败'
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '下载超时，请检查网络后重试'
          } else if (err.errMsg.includes('network')) {
            errorMessage = '网络错误，请检查网络连接'
          } else if (err.errMsg.includes('url not in domain list')) {
            errorMessage = '视频链接无效，请重新生成'
          } else {
            errorMessage = `下载失败: ${err.errMsg}`
          }
        }

        uni.showModal({
          title: '下载失败',
          content: errorMessage,
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              // 用户选择重试
              setTimeout(() => {
                downloadVideo()
              }, 500)
            }
          }
        })
      }
    })

  } catch (error) {
    console.error('下载视频出错:', error)
    uni.hideLoading()
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding-bottom: 120rpx;
}

/* 现代化头部区域 */
.hero-header {
  position: relative;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  padding: 60rpx 32rpx 80rpx;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.9) 0%, 
    rgba(79, 70, 229, 0.9) 50%, 
    rgba(139, 92, 246, 0.9) 100%);
  opacity: 0.95;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.success-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.success-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.check-icon {
  font-size: 64rpx;
  color: white;
  font-weight: bold;
}

.success-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

.success-content {
  color: white;
}

.success-title {
  display: block;
  font-size: 42rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.success-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 内容区域 */
.content-wrapper {
  padding: 32rpx;
  margin-top: -40rpx;
  position: relative;
  z-index: 3;
}

.media-card, .subtitle-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.08);
  overflow: hidden;
  border: 1rpx solid rgba(99, 102, 241, 0.08);
}

.card-header {
  padding: 32rpx 32rpx 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}

.card-badge {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.subtitle-count {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(99, 102, 241, 0.1);
}

.count-icon {
  font-size: 20rpx;
}

.count-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #6366f1;
}

.card-divider {
  height: 2rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  margin-bottom: 24rpx;
  border-radius: 2rpx;
}

/* 视频区域 */
.video-wrapper {
  padding: 0 32rpx 32rpx;
}

.video-container {
  aspect-ratio: 16/9;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.video-placeholder {
  text-align: center;
  color: #6b7280;
}

.loading-animation {
  position: relative;
  margin-bottom: 24rpx;
}

.loading-icon {
  font-size: 64rpx;
  animation: bounce 1.5s infinite;
}

.pulse-ring {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  border: 3rpx solid #6366f1;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-ring 2s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes pulse-ring {
  0% { transform: scale(0.8); opacity: 0.5; }
  100% { transform: scale(1.2); opacity: 0; }
}

.loading-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 字幕区域 */
.subtitle-scroll {
  max-height: 500rpx;
  padding: 0 32rpx 32rpx;
  box-sizing: border-box;
}

.subtitle-list {
  padding: 0;
}

.subtitle-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
  gap: 20rpx;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-index {
  flex-shrink: 0;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4rpx;
}

.index-number {
  color: white;
  font-size: 22rpx;
  font-weight: 600;
}

.subtitle-content {
  flex: 1;
  min-width: 0; /* 确保flex item可以收缩 */
  overflow: hidden;
}

.subtitle-time {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
}

.empty-subtitle {
  text-align: center;
  padding: 80rpx 32rpx;
  color: #9ca3af;
}

.empty-icon {
  margin-bottom: 24rpx;
}

.empty-emoji {
  font-size: 64rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部操作区 */
.action-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 32rpx;
  border-top: 1rpx solid rgba(99, 102, 241, 0.1);
  z-index: 100;
}

.download-btn {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 20rpx;
  padding: 28rpx;
  color: white;
  font-weight: 600;
  font-size: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.download-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.4);
}
</style>
